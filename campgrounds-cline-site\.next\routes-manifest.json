{"version": 3, "caseSensitive": false, "basePath": "", "rewrites": {"beforeFiles": [], "afterFiles": [], "fallback": []}, "redirects": [{"source": "/:file((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+)/", "destination": "/:file", "permanent": true, "internal": true, "missing": [{"type": "header", "key": "x-nextjs-data"}], "regex": "^(?:\\/((?!\\.well-known(?:\\/.*)?)(?:[^/]+\\/)*[^/]+\\.\\w+))\\/$"}, {"source": "/:notfile((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+)", "destination": "/:notfile/", "permanent": true, "internal": true, "regex": "^(?:\\/((?!\\.well-known(?:\\/.*)?)(?:[^/]+\\/)*[^/\\.]+))$"}, {"source": "/campgrounds", "destination": "/states", "permanent": true, "regex": "^(?!\\/_next)\\/campgrounds(?:\\/)?$"}, {"source": "/rv-parks", "destination": "/amenities/full-hookups", "permanent": true, "regex": "^(?!\\/_next)\\/rv-parks(?:\\/)?$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}], "regex": "^(?:\\/(.*))(?:\\/)?$"}]}