import { RESTART_EXIT_CODE } from '../../server/lib/utils';
import { middlewareResponse } from './middleware-response';
import { invalidatePersistentCache as invalidateWebpackPersistentCache } from '../../build/webpack/cache-invalidation';
const EVENT_DEV_OVERLAY_RESTART_SERVER = 'DEV_OVERLAY_RESTART_SERVER';
export function getRestartDevServerMiddleware(param) {
    let { telemetry, turbopackProject, webpackCacheDirectories } = param;
    /**
   * Some random value between 1 and Number.MAX_SAFE_INTEGER (inclusive). The same value is returned
   * on every call to `__nextjs_server_status` until the server is restarted.
   *
   * Can be used to determine if two server status responses are from the same process or a
   * different (restarted) process.
   */ const executionId = Math.floor(Math.random() * Number.MAX_SAFE_INTEGER) + 1;
    async function handleRestartRequest(req, res, searchParams) {
        if (req.method !== 'POST') {
            return middlewareResponse.methodNotAllowed(res);
        }
        const shouldInvalidatePersistentCache = searchParams.has('invalidatePersistentCache');
        if (shouldInvalidatePersistentCache) {
            if (webpackCacheDirectories != null) {
                await Promise.all(Array.from(webpackCacheDirectories).map(invalidateWebpackPersistentCache));
            }
            if (turbopackProject != null) {
                await turbopackProject.invalidatePersistentCache();
            }
        }
        telemetry.record({
            eventName: EVENT_DEV_OVERLAY_RESTART_SERVER,
            payload: {
                invalidatePersistentCache: shouldInvalidatePersistentCache
            }
        });
        // TODO: Use flushDetached
        await telemetry.flush();
        // do this async to try to give the response a chance to send
        // it's not really important if it doesn't though
        setTimeout(()=>{
            process.exit(RESTART_EXIT_CODE);
        }, 0);
        return middlewareResponse.noContent(res);
    }
    async function handleServerStatus(req, res) {
        if (req.method !== 'GET') {
            return middlewareResponse.methodNotAllowed(res);
        }
        return middlewareResponse.json(res, {
            executionId
        });
    }
    return async function(req, res, next) {
        const { pathname, searchParams } = new URL("http://n" + req.url);
        switch(pathname){
            case '/__nextjs_restart_dev':
                return await handleRestartRequest(req, res, searchParams);
            case '/__nextjs_server_status':
                return await handleServerStatus(req, res);
            default:
                return next();
        }
    };
}

//# sourceMappingURL=restart-dev-server-middleware.js.map