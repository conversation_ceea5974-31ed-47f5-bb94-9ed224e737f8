/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable static export for GitHub Pages deployment
  output: 'export',
  trailingSlash: true,
  
  // Disable image optimization for static export
  images: {
    unoptimized: true
  },
  
  // ISR configuration for when not using static export
  experimental: {
    isrMemoryCacheSize: 0, // Disable memory cache for large datasets
  },
  
  // Build configuration
  generateBuildId: async () => {
    return 'campgrounds-build-' + Date.now()
  },
  
  // Optimize for production
  swcMinify: true,
  
  // Environment variables
  env: {
    SITE_URL: process.env.NEXT_PUBLIC_SITE_URL || 'https://campgroundsdirect.com',
    SITE_NAME: 'CampgroundsDirect'
  },
  
  // Webpack configuration for better builds
  webpack: (config, { isServer }) => {
    // Optimize for large datasets
    config.optimization = {
      ...config.optimization,
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          default: {
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true
          },
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            priority: -10,
            chunks: 'all'
          }
        }
      }
    }
    
    return config
  },
  
  // Redirects for SEO
  async redirects() {
    return [
      {
        source: '/campgrounds',
        destination: '/states',
        permanent: true,
      },
      {
        source: '/rv-parks',
        destination: '/amenities/full-hookups',
        permanent: true,
      }
    ]
  },
  
  // Headers for better performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin'
          }
        ]
      }
    ]
  }
};

module.exports = nextConfig;
